# Multibot Delayed Reaction System - Setup Instructions

## Overview
This system implements a sophisticated delayed reaction mechanism for Telegram bots with queue management and persistent storage.

## System Components

### 1. Core Files
- `bot.php` - Main webhook handler (modified)
- `queue_manager.php` - Queue management and persistent storage
- `reaction_processor.php` - Background processor for delayed reactions
- `monitor.php` - Web-based monitoring interface

### 2. Channel Configurations

#### Channel -1002140916089 (Sequential Immediate)
- Sequential emoji assignment using modulo logic
- Immediate reactions
- Emojis: ['❤️‍🔥', '🐳', '🦄', '💯', '🔥']

#### Channel -1002888471861 (Random Delayed)
- Random emoji selection from extended pool
- 20-second delays between bot reactions
- Queue management for multiple posts
- Emojis: ['❤️','👍','🔥','🥰','👏','🤩','💯','⚡️','🏆','😘','😎']

#### All Other Channels
- Original behavior maintained
- Immediate reactions

## Installation Steps

### 1. File Permissions
```bash
chmod 755 *.php
chmod 755 queue_data/
```

### 2. Create Required Directories
```bash
mkdir -p queue_data
chmod 755 queue_data
```

### 3. Start the Background Processor

#### Option A: Direct Command Line
```bash
# Start the processor
php reaction_processor.php start

# Check status
php reaction_processor.php status

# Stop the processor
php reaction_processor.php stop
```

#### Option B: Using Screen (Recommended for Production)
```bash
# Start in a screen session
screen -S multibot-processor
php reaction_processor.php start

# Detach from screen: Ctrl+A, then D
# Reattach later: screen -r multibot-processor
```

#### Option C: Using nohup
```bash
nohup php reaction_processor.php start > processor.log 2>&1 &
```

### 4. Set Up Cron Job for Auto-Restart
Add to crontab (`crontab -e`):
```bash
# Check every 5 minutes if processor is running, restart if needed
*/5 * * * * /usr/bin/php /path/to/your/multibot/reaction_processor.php start >/dev/null 2>&1

# Clean up old logs daily at 2 AM
0 2 * * * /usr/bin/php /path/to/your/multibot/monitor.php?action=cleanup >/dev/null 2>&1
```

### 5. Web Server Configuration

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteRule ^monitor$ monitor.php [L]
RewriteRule ^status$ monitor.php?action=status [L]
```

#### Nginx
```nginx
location /monitor {
    try_files $uri /monitor.php;
}
location /status {
    try_files $uri /monitor.php?action=status;
}
```

## Monitoring and Management

### 1. Web Interface
Access the monitoring interface at:
- `http://yourserver.com/monitor.php` - Full status page
- `http://yourserver.com/monitor.php?action=status` - JSON status API

### 2. Command Line Monitoring
```bash
# Check processor status
php reaction_processor.php status

# View recent logs
tail -f delayed_reactions.log
tail -f bot_reactions.log

# Check queue files
ls -la queue_data/
```

### 3. Log Files
- `bot_reactions.log` - All immediate reactions
- `delayed_reactions.log` - Background processor activity
- `queue_data/queue.json` - Current queue state
- `queue_data/progress.json` - Reaction progress tracking

## Troubleshooting

### 1. Processor Not Starting
```bash
# Check if already running
ps aux | grep reaction_processor

# Check for permission issues
ls -la processor.pid
ls -la queue_data/

# Check PHP CLI
php -v
```

### 2. Queue Issues
```bash
# Check queue directory permissions
ls -la queue_data/

# Manually clean stuck posts
php -r "require 'queue_manager.php'; $qm = new QueueManager(); echo $qm->cleanupOldPosts();"
```

### 3. Memory Issues
```bash
# Check memory usage
ps aux | grep reaction_processor

# Monitor log file sizes
du -sh *.log
```

## Performance Considerations

### 1. Server Resources
- Minimum 512MB RAM recommended
- PHP CLI access required
- Persistent storage (not tmpfs)

### 2. Scaling
- Each post takes ~14.5 minutes to complete (44 bots × 20 seconds)
- Queue system handles multiple concurrent posts
- Monitor queue depth during high activity

### 3. Error Recovery
- Automatic retry on failed API calls
- Graceful handling of server restarts
- Persistent queue survives reboots

## Security Notes

### 1. File Permissions
- Ensure queue_data/ is not web-accessible
- Protect log files from public access
- Use proper file permissions (755 for directories, 644 for files)

### 2. Process Management
- Run processor under dedicated user account
- Monitor for zombie processes
- Implement log rotation

## Maintenance Tasks

### Daily
- Check processor status
- Monitor queue depth
- Review error logs

### Weekly
- Clean up old log files
- Check disk space usage
- Verify cron jobs are running

### Monthly
- Archive old queue data
- Review system performance
- Update bot tokens if needed

## Emergency Procedures

### 1. Stop All Processing
```bash
php reaction_processor.php stop
pkill -f reaction_processor
```

### 2. Clear Queue
```bash
rm -f queue_data/queue.json
rm -f queue_data/progress.json
```

### 3. Reset System
```bash
php reaction_processor.php stop
rm -f processor.pid
rm -f queue_data/*.json
php reaction_processor.php start
```

## Support and Debugging

### Enable Debug Mode
Add to reaction_processor.php:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Increase Logging
Modify log levels in queue_manager.php and reaction_processor.php for more detailed output.

### Contact Information
- Monitor logs for error patterns
- Check system resources during high load
- Verify Telegram API rate limits
