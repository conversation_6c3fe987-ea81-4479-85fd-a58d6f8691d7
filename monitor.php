<?php
/**
 * Monitoring and Management Interface for Multibot Delayed Reaction System
 * Provides status monitoring, error reporting, and manual queue management
 */

require_once 'queue_manager.php';

class MultibotMonitor {
    private $queueManager;
    private $logFiles;
    
    public function __construct() {
        $this->queueManager = new QueueManager();
        $this->logFiles = [
            'reactions' => __DIR__ . '/bot_reactions.log',
            'delayed' => __DIR__ . '/delayed_reactions.log',
            'errors' => __DIR__ . '/error.log'
        ];
    }
    
    /**
     * Get comprehensive system status
     */
    public function getSystemStatus() {
        $status = [
            'timestamp' => date('Y-m-d H:i:s'),
            'queue' => $this->queueManager->getQueueStatus(),
            'processor' => $this->getProcessorStatus(),
            'recent_activity' => $this->getRecentActivity(),
            'error_summary' => $this->getErrorSummary()
        ];
        
        return $status;
    }
    
    /**
     * Check if the reaction processor is running
     */
    private function getProcessorStatus() {
        $pidFile = __DIR__ . '/processor.pid';
        
        if (!file_exists($pidFile)) {
            return ['status' => 'stopped', 'pid' => null];
        }
        
        $pid = file_get_contents($pidFile);
        if (!$pid) {
            return ['status' => 'stopped', 'pid' => null];
        }
        
        // Check if process is actually running
        $isRunning = false;
        if (function_exists('posix_kill')) {
            $isRunning = posix_kill($pid, 0);
        } else {
            // Fallback for systems without posix functions
            $output = shell_exec("ps -p {$pid} 2>/dev/null");
            $isRunning = strpos($output, $pid) !== false;
        }
        
        return [
            'status' => $isRunning ? 'running' : 'stopped',
            'pid' => $isRunning ? $pid : null
        ];
    }
    
    /**
     * Get recent activity from logs
     */
    private function getRecentActivity($lines = 20) {
        $activity = [];
        
        foreach ($this->logFiles as $type => $file) {
            if (file_exists($file)) {
                $content = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                $recentLines = array_slice($content, -$lines);
                $activity[$type] = $recentLines;
            } else {
                $activity[$type] = [];
            }
        }
        
        return $activity;
    }
    
    /**
     * Get error summary from logs
     */
    private function getErrorSummary() {
        $errors = [];
        $errorPatterns = [
            'failed' => '/FAILED|ERROR|error/i',
            'timeout' => '/timeout|timed out/i',
            'connection' => '/connection|network|curl/i'
        ];
        
        foreach ($this->logFiles as $type => $file) {
            if (!file_exists($file)) continue;
            
            $content = file_get_contents($file);
            $lines = explode("\n", $content);
            
            foreach ($errorPatterns as $errorType => $pattern) {
                $matches = preg_grep($pattern, $lines);
                if (!empty($matches)) {
                    $errors[$type][$errorType] = count($matches);
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Clean up old log files
     */
    public function cleanupLogs($daysToKeep = 7) {
        $cutoffTime = time() - ($daysToKeep * 24 * 60 * 60);
        $cleaned = 0;
        
        foreach ($this->logFiles as $type => $file) {
            if (file_exists($file) && filemtime($file) < $cutoffTime) {
                // Archive old log instead of deleting
                $archiveName = $file . '.' . date('Y-m-d', filemtime($file));
                if (rename($file, $archiveName)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Force cleanup of stuck posts
     */
    public function cleanupStuckPosts($maxAge = 3600) {
        // This would require extending QueueManager with stuck post detection
        // For now, just return the cleanup count from QueueManager
        return $this->queueManager->cleanupOldPosts();
    }
    
    /**
     * Get detailed queue information
     */
    public function getDetailedQueueInfo() {
        $queueFile = __DIR__ . '/queue_data/queue.json';
        $progressFile = __DIR__ . '/queue_data/progress.json';
        
        $queue = $this->loadJsonFile($queueFile, []);
        $progress = $this->loadJsonFile($progressFile, []);
        
        $detailed = [];
        foreach ($progress as $postId => $postData) {
            $detailed[] = [
                'post_id' => $postId,
                'chat_id' => $postData['chat_id'],
                'message_id' => $postData['message_id'],
                'status' => $postData['status'],
                'current_bot' => $postData['current_bot'],
                'reactions_sent' => count($postData['reactions_sent']),
                'created_at' => date('Y-m-d H:i:s', $postData['created_at']),
                'next_reaction_time' => date('Y-m-d H:i:s', $postData['next_reaction_time']),
                'time_until_next' => max(0, $postData['next_reaction_time'] - time())
            ];
        }
        
        return $detailed;
    }
    
    /**
     * Load JSON file helper
     */
    private function loadJsonFile($filename, $default = []) {
        if (!file_exists($filename)) {
            return $default;
        }
        
        $content = file_get_contents($filename);
        if ($content === false) {
            return $default;
        }
        
        $data = json_decode($content, true);
        return $data !== null ? $data : $default;
    }
    
    /**
     * Generate HTML status page
     */
    public function generateStatusPage() {
        $status = $this->getSystemStatus();
        $detailedQueue = $this->getDetailedQueueInfo();
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Multibot System Monitor</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .status-good { color: #28a745; }
                .status-warning { color: #ffc107; }
                .status-error { color: #dc3545; }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background-color: #f8f9fa; }
                .log-entry { font-family: monospace; font-size: 12px; margin: 2px 0; }
                .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            </style>
            <script>
                function refreshPage() { location.reload(); }
                setInterval(refreshPage, 30000); // Auto-refresh every 30 seconds
            </script>
        </head>
        <body>
            <div class="container">
                <h1>Multibot System Monitor</h1>
                <p>Last updated: <?= $status['timestamp'] ?> <button class="refresh-btn" onclick="refreshPage()">Refresh</button></p>
                
                <div class="grid">
                    <div class="card">
                        <h3>System Status</h3>
                        <p>Processor: <span class="<?= $status['processor']['status'] === 'running' ? 'status-good' : 'status-error' ?>">
                            <?= strtoupper($status['processor']['status']) ?>
                        </span></p>
                        <?php if ($status['processor']['pid']): ?>
                            <p>PID: <?= $status['processor']['pid'] ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card">
                        <h3>Queue Statistics</h3>
                        <p>Queued Posts: <?= $status['queue']['queued_posts'] ?></p>
                        <p>Active Posts: <?= $status['queue']['active_posts'] ?></p>
                        <p>Completed Posts: <?= $status['queue']['completed_posts'] ?></p>
                        <p>Total Posts: <?= $status['queue']['total_posts'] ?></p>
                    </div>
                </div>
                
                <?php if (!empty($detailedQueue)): ?>
                <div class="card">
                    <h3>Active Queue Details</h3>
                    <table>
                        <tr>
                            <th>Post ID</th>
                            <th>Status</th>
                            <th>Current Bot</th>
                            <th>Reactions Sent</th>
                            <th>Next Reaction</th>
                            <th>Time Until Next</th>
                        </tr>
                        <?php foreach ($detailedQueue as $item): ?>
                        <tr>
                            <td><?= htmlspecialchars($item['post_id']) ?></td>
                            <td><?= htmlspecialchars($item['status']) ?></td>
                            <td><?= $item['current_bot'] ?>/44</td>
                            <td><?= $item['reactions_sent'] ?></td>
                            <td><?= $item['next_reaction_time'] ?></td>
                            <td><?= $item['time_until_next'] ?>s</td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </div>
                <?php endif; ?>
                
                <div class="card">
                    <h3>Recent Activity</h3>
                    <h4>Delayed Reactions</h4>
                    <div style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px;">
                        <?php foreach (array_reverse($status['recent_activity']['delayed']) as $line): ?>
                            <div class="log-entry"><?= htmlspecialchars($line) ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
}

// Web interface
if (isset($_GET['action'])) {
    $monitor = new MultibotMonitor();
    
    switch ($_GET['action']) {
        case 'status':
            header('Content-Type: application/json');
            echo json_encode($monitor->getSystemStatus());
            break;
            
        case 'cleanup':
            $cleaned = $monitor->cleanupStuckPosts();
            header('Content-Type: application/json');
            echo json_encode(['cleaned' => $cleaned]);
            break;
            
        default:
            header('Content-Type: text/html');
            echo $monitor->generateStatusPage();
            break;
    }
} else {
    // Default: show status page
    $monitor = new MultibotMonitor();
    header('Content-Type: text/html');
    echo $monitor->generateStatusPage();
}
