@echo off
REM Multibot Delayed Reaction Processor Startup Script for Windows
REM This script ensures the processor is running and handles restarts

setlocal enabledelayedexpansion

set SCRIPT_DIR=%~dp0
set PROCESSOR_SCRIPT=%SCRIPT_DIR%reaction_processor.php
set PID_FILE=%SCRIPT_DIR%processor.pid
set LOG_FILE=%SCRIPT_DIR%processor_startup.log

REM Function to log messages
:log_message
echo [%date% %time%] %~1 >> "%LOG_FILE%"
echo [%date% %time%] %~1
goto :eof

REM Function to check if processor is running
:is_processor_running
if not exist "%PID_FILE%" (
    set PROCESSOR_RUNNING=0
    goto :eof
)

set /p PID=<"%PID_FILE%"
tasklist /FI "PID eq %PID%" 2>nul | find /I "%PID%" >nul
if %errorlevel% equ 0 (
    set PROCESSOR_RUNNING=1
) else (
    del "%PID_FILE%" 2>nul
    set PROCESSOR_RUNNING=0
)
goto :eof

REM Function to start the processor
:start_processor
call :log_message "Starting delayed reaction processor..."

cd /d "%SCRIPT_DIR%"

REM Start processor in background using start command
start /B php "%PROCESSOR_SCRIPT%" start >> "%LOG_FILE%" 2>&1

REM Wait a moment for startup
timeout /t 3 /nobreak >nul

REM Check if it started successfully
call :is_processor_running
if !PROCESSOR_RUNNING! equ 1 (
    set /p PID=<"%PID_FILE%"
    call :log_message "Processor started successfully (PID: !PID!)"
    set START_SUCCESS=1
) else (
    call :log_message "Failed to start processor"
    set START_SUCCESS=0
)
goto :eof

REM Function to stop the processor
:stop_processor
call :is_processor_running
if !PROCESSOR_RUNNING! equ 1 (
    set /p PID=<"%PID_FILE%"
    call :log_message "Stopping processor (PID: !PID!)..."
    
    REM Kill the process
    taskkill /PID !PID! /F >nul 2>&1
    
    REM Clean up PID file
    del "%PID_FILE%" 2>nul
    call :log_message "Processor stopped"
) else (
    call :log_message "Processor is not running"
)
goto :eof

REM Function to show status
:show_status
call :is_processor_running
if !PROCESSOR_RUNNING! equ 1 (
    set /p PID=<"%PID_FILE%"
    echo Processor is running (PID: !PID!)
    
    REM Show queue status if available
    if exist "%PROCESSOR_SCRIPT%" (
        echo Queue status:
        php "%PROCESSOR_SCRIPT%" status
    )
) else (
    echo Processor is not running
)
goto :eof

REM Main script logic
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=start

if "%COMMAND%"=="start" (
    call :is_processor_running
    if !PROCESSOR_RUNNING! equ 1 (
        echo Processor is already running
        exit /b 0
    ) else (
        call :start_processor
        if !START_SUCCESS! equ 1 (
            exit /b 0
        ) else (
            exit /b 1
        )
    )
) else if "%COMMAND%"=="stop" (
    call :stop_processor
) else if "%COMMAND%"=="restart" (
    call :log_message "Restarting processor..."
    call :stop_processor
    timeout /t 2 /nobreak >nul
    call :start_processor
) else if "%COMMAND%"=="status" (
    call :show_status
) else if "%COMMAND%"=="force-start" (
    REM Force start even if PID file exists
    del "%PID_FILE%" 2>nul
    call :start_processor
) else (
    echo Usage: %0 {start^|stop^|restart^|status^|force-start}
    echo.
    echo Commands:
    echo   start       - Start the processor if not running
    echo   stop        - Stop the processor
    echo   restart     - Restart the processor
    echo   status      - Show processor and queue status
    echo   force-start - Force start (remove stale PID file^)
    exit /b 1
)
