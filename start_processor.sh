#!/bin/bash

# Multibot Delayed Reaction Processor Startup Script
# This script ensures the processor is running and handles restarts

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROCESSOR_SCRIPT="$SCRIPT_DIR/reaction_processor.php"
PID_FILE="$SCRIPT_DIR/processor.pid"
LOG_FILE="$SCRIPT_DIR/processor_startup.log"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to check if processor is running
is_processor_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # Running
        else
            # PID file exists but process is not running
            rm -f "$PID_FILE"
            return 1  # Not running
        fi
    else
        return 1  # Not running
    fi
}

# Function to start the processor
start_processor() {
    log_message "Starting delayed reaction processor..."
    
    # Change to script directory
    cd "$SCRIPT_DIR"
    
    # Start processor in background
    nohup php "$PROCESSOR_SCRIPT" start >> "$LOG_FILE" 2>&1 &
    
    # Wait a moment for startup
    sleep 2
    
    # Check if it started successfully
    if is_processor_running; then
        local pid=$(cat "$PID_FILE")
        log_message "Processor started successfully (PID: $pid)"
        return 0
    else
        log_message "Failed to start processor"
        return 1
    fi
}

# Function to stop the processor
stop_processor() {
    if is_processor_running; then
        local pid=$(cat "$PID_FILE")
        log_message "Stopping processor (PID: $pid)..."
        
        # Send TERM signal
        kill -TERM "$pid" 2>/dev/null
        
        # Wait for graceful shutdown
        local count=0
        while [ $count -lt 10 ] && ps -p "$pid" > /dev/null 2>&1; do
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "Force killing processor..."
            kill -KILL "$pid" 2>/dev/null
        fi
        
        # Clean up PID file
        rm -f "$PID_FILE"
        log_message "Processor stopped"
    else
        log_message "Processor is not running"
    fi
}

# Function to restart the processor
restart_processor() {
    log_message "Restarting processor..."
    stop_processor
    sleep 2
    start_processor
}

# Function to show status
show_status() {
    if is_processor_running; then
        local pid=$(cat "$PID_FILE")
        echo "Processor is running (PID: $pid)"
        
        # Show queue status if available
        if [ -f "$PROCESSOR_SCRIPT" ]; then
            echo "Queue status:"
            php "$PROCESSOR_SCRIPT" status
        fi
    else
        echo "Processor is not running"
    fi
}

# Function to monitor and auto-restart
monitor_processor() {
    log_message "Starting processor monitor..."
    
    while true; do
        if ! is_processor_running; then
            log_message "Processor not running, attempting to start..."
            start_processor
        fi
        
        # Check every 60 seconds
        sleep 60
    done
}

# Main script logic
case "${1:-start}" in
    start)
        if is_processor_running; then
            echo "Processor is already running"
            exit 0
        else
            start_processor
            exit $?
        fi
        ;;
    
    stop)
        stop_processor
        ;;
    
    restart)
        restart_processor
        ;;
    
    status)
        show_status
        ;;
    
    monitor)
        monitor_processor
        ;;
    
    force-start)
        # Force start even if PID file exists
        rm -f "$PID_FILE"
        start_processor
        ;;
    
    *)
        echo "Usage: $0 {start|stop|restart|status|monitor|force-start}"
        echo ""
        echo "Commands:"
        echo "  start       - Start the processor if not running"
        echo "  stop        - Stop the processor"
        echo "  restart     - Restart the processor"
        echo "  status      - Show processor and queue status"
        echo "  monitor     - Start monitoring mode (auto-restart)"
        echo "  force-start - Force start (remove stale PID file)"
        exit 1
        ;;
esac
