<?php
/**
 * Delayed Reaction Processor for Multibot System
 * Background process that handles sequential delayed reactions with 20-second intervals
 */

require_once 'queue_manager.php';

class ReactionProcessor {
    private $queueManager;
    private $botTokens;
    private $logFile;
    private $isRunning;
    private $pidFile;
    
    public function __construct() {
        $this->queueManager = new QueueManager();
        $this->logFile = __DIR__ . '/delayed_reactions.log';
        $this->pidFile = __DIR__ . '/processor.pid';
        $this->isRunning = false;
        
        // Load bot tokens from main configuration
        $this->loadBotTokens();
        
        // Set up signal handlers for graceful shutdown
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'handleShutdown']);
            pcntl_signal(SIGINT, [$this, 'handleShutdown']);
        }
    }
    
    /**
     * Load bot tokens from the main bot configuration
     */
    private function loadBotTokens() {
        $this->botTokens = [
            '1'  => '7324085708:AAECLspc6xqyZZ7lekSsm1ZS6PDhsWRejAE',
            '2'  => '7734048562:AAFPk0NBRHwGyFb-hDtt_Br9i6HlZ5IKd1g',
            '3'  => '8000823167:AAG139icfPTOrXUDbR1lEtYOBsJ2IqzcU7E',
            '4'  => '8121324542:AAFsEWUTlbX4_BcaTE8XAoA2WYO2sxHhwKQ',
            '5'  => '7043095943:AAGhPspmxSlvT1yzT9ARIcT3FFr9pO5FlxI',
            '6'  => '8070640534:AAF0SzNYuqxOQ8DvoIzpgA9LnNjAGWE9gho',
            '7'  => '7414728357:AAFWdAq0faNm2Yczhm0qNAX0-Tqo1u6jrAQ',
            '8'  => '7595655197:AAE-LVH3ao4rRRCeOMHdnqw_hgv7tMxPbiI',
            '9'  => '7631470660:AAGF6R8_udL0tWVqZWTFF43Z8wdYQoRDYSU',
            '10' => '8181651796:AAE2aVmc52tPVBofhzj5RbVvPNZKWQiYL3o',
            '11' => '7807349163:AAGr1S0OS4O09YP-PKxVTXQwh5fgss3VdS4',
            '12' => '7224250149:AAFg8tIbi8xsJHVLZSVv52NHtAfxyJ7p7IA',
            '13' => '7339986151:AAETfpuU3ksUtk5GSkLkPsrb9tGiccSb9dg',
            '14' => '7748840812:AAHDjQVZL1NAkHUMUYGuBqu0sAudlXEc2Uo',
            '15' => '7728027518:AAEBUOi_BPYqqu8qXOvYQfM9GHfVUDwJ0-M',
            '16' => '7601378202:AAHi2Jo0t_W8sEWnxWkDlZqjYAwpUGimG6k',
            '17' => '8005056905:AAGBc7HjP4gvAlrnKrelz94A4mNSgDBqLmg',
            '18' => '5920970906:AAEl59pHfUw0WnqkPnR5VH4HWq35mL_m0u0',
            '19' => '7957888180:AAEsxFpBqlwI1snuXdLxBM9feYo8GrxL71U',
            '20' => '1874727878:AAGhz33r6JB7jf5LA5GmXKuu-NkKE-juO4Y',
            '21' => '1782934509:AAHAvy6rIDj9WOi3N6Waz_MbSbzghrDBsb0',
            '22' => '1854747800:AAFrh45kpaQ7UOMx7rF4YOADrFCh2P3x5Fs',
            '23' => '1812296269:AAE8KpwmmO73Bg9uIxgiihvhz8wd6P-0-ww',
            '24' => '1833448524:AAE1C4jzssdPGA5iYlmrNtWchE6I0E6gURw',
            '25' => '2054730466:AAGaTrFU4UvA2qrt2vmUTBB72G1UFzg7MXk',
            '26' => '2103424776:AAGkaXs8NqiFjfoXJ7G2RPzNMnhpLXCJG98',
            '27' => '5232551378:AAEA6kDOIAUPhl44Eqzo_jTJNV09cHa3Ifg',
            '28' => '5076605250:AAFIwFnhMd16iW27l5SOszE1KVU_X3dLbA4',
            '29' => '5116250996:AAG-a_xoeczvkCr_Z_Ggus9Fu_C49OqgmZ4',
            '30' => '7710186495:AAF46DoNSUQ9PEEDwREx8aOK42Du1rhyXoE',
            '31' => '5989552141:AAGEQ1ObcCmaO68EvwU3XFMRuRHODSAv2o0',
            '32' => '5954755638:AAHyHoMUMmkTgFON6Cwzkq4Pu0bXMVrZKo4',
            '33' => '7638711403:AAFlCDzFZohgrW-duuPiijRMRT-BtKO9a2s',
            '34' => '7992531992:AAHgqBoygZ-vEvGiihwGuaMwG5d3v3BQ-TI',
            '35' => '7680297842:AAGPmB2yufn-n6-xNLIBag5eYEchh6pIF48',
            '36' => '7529351450:AAGFFKB4VadwrZTZjjyqBhuS-bCDMi2fB0s',
            '37' => '7611442057:AAGnhvfJg_EnOITI5mrEhN8p5ehGgiM1ax0',
            '38' => '7496411088:AAF5_Hz4nysgtimtZ0xo1-QcLqFn4dAyEh0',
            '39' => '8171836495:AAFUHd4K7OcuCfnXVqpEoQFBLd4bhxf4maA',
            '40' => '7309785917:AAEDGLAsBw5yEraoPhSO_PfYCkvSyNwGHts',
            '41' => '7464545372:AAGiey-QGPxpfVnBMB3eaMe-MkBV5JVAJus',
            '42' => '7504410338:AAF7rlo9UZAAwiGl6WMwQ8DqQAfQGCTii-o',
            '43' => '7611709153:AAGEjbgr-KZcyDMn7Z5dfQwZZPgLSHssp6Y',
            '44' => '8000444345:AAHicXHhUnRptpI3yp26MB0Af_9rWC9t3QE',
        ];
    }
    
    /**
     * Start the processor daemon
     */
    public function start() {
        // Check if already running
        if ($this->isProcessRunning()) {
            $this->log("Processor is already running (PID: " . file_get_contents($this->pidFile) . ")");
            return false;
        }
        
        // Write PID file
        file_put_contents($this->pidFile, getmypid());
        $this->isRunning = true;
        
        $this->log("Delayed reaction processor started (PID: " . getmypid() . ")");
        
        // Main processing loop
        while ($this->isRunning) {
            try {
                $this->processNextReaction();
                
                // Clean up old posts periodically (every hour)
                if (time() % 3600 < 5) {
                    $cleaned = $this->queueManager->cleanupOldPosts();
                    if ($cleaned > 0) {
                        $this->log("Cleaned up {$cleaned} old completed posts");
                    }
                }
                
                // Check for signals
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }
                
                // Sleep for 1 second before next check
                sleep(1);
                
            } catch (Exception $e) {
                $this->log("Error in processing loop: " . $e->getMessage());
                sleep(5); // Wait longer on error
            }
        }
        
        $this->cleanup();
    }
    
    /**
     * Process the next ready reaction
     */
    private function processNextReaction() {
        $nextPost = $this->queueManager->getNextReadyPost();
        
        if (!$nextPost) {
            return; // Nothing ready to process
        }
        
        $botId = $nextPost['current_bot'];
        $chatId = $nextPost['chat_id'];
        $messageId = $nextPost['message_id'];
        $emojiPool = $nextPost['emoji_pool'];
        
        // Skip if bot ID is invalid
        if ($botId > 44 || !isset($this->botTokens[(string)$botId])) {
            $this->log("Invalid bot ID {$botId} for post {$nextPost['post_id']}");
            $this->queueManager->markReactionSent($nextPost['post_id'], $botId, '', false);
            return;
        }
        
        // Select random emoji
        $emoji = $emojiPool[array_rand($emojiPool)];
        
        // Send reaction
        $success = $this->sendReaction($botId, $chatId, $messageId, $emoji);
        
        // Update progress
        $this->queueManager->markReactionSent($nextPost['post_id'], $botId, $emoji, $success);
        
        $status = $success ? 'SUCCESS' : 'FAILED';
        $this->log("Bot #{$botId} → '{$emoji}' for post {$nextPost['post_id']} [{$status}]");
    }
    
    /**
     * Send reaction via Telegram API
     */
    private function sendReaction($botId, $chatId, $messageId, $emoji) {
        $botToken = $this->botTokens[(string)$botId];
        $apiURL = "https://api.telegram.org/bot{$botToken}/";
        
        $url = $apiURL . "setMessageReaction"
             . "?chat_id={$chatId}"
             . "&message_id={$messageId}"
             . "&reaction=[{\"type\":\"emoji\",\"emoji\":\"{$emoji}\"}]"
             . "&is_big=true";
        
        // Use cURL for better error handling
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->log("cURL error for bot {$botId}: {$error}");
            return false;
        }
        
        if ($httpCode !== 200) {
            $this->log("HTTP error {$httpCode} for bot {$botId}: {$response}");
            return false;
        }
        
        $result = json_decode($response, true);
        return isset($result['ok']) && $result['ok'] === true;
    }
    
    /**
     * Check if processor is already running
     */
    private function isProcessRunning() {
        if (!file_exists($this->pidFile)) {
            return false;
        }
        
        $pid = file_get_contents($this->pidFile);
        if (!$pid) {
            return false;
        }
        
        // Check if process is actually running
        if (function_exists('posix_kill')) {
            return posix_kill($pid, 0);
        }
        
        // Fallback for systems without posix functions
        $output = shell_exec("ps -p {$pid}");
        return strpos($output, $pid) !== false;
    }
    
    /**
     * Handle shutdown signals
     */
    public function handleShutdown() {
        $this->log("Received shutdown signal, stopping processor...");
        $this->isRunning = false;
    }
    
    /**
     * Stop the processor
     */
    public function stop() {
        $this->isRunning = false;
        $this->cleanup();
    }
    
    /**
     * Cleanup on shutdown
     */
    private function cleanup() {
        if (file_exists($this->pidFile)) {
            unlink($this->pidFile);
        }
        $this->log("Delayed reaction processor stopped");
    }
    
    /**
     * Log message with timestamp
     */
    private function log($message) {
        $timestamp = date('[Y-m-d H:i:s]');
        $logEntry = "{$timestamp} {$message}\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also output to console if running in CLI
        if (php_sapi_name() === 'cli') {
            echo $logEntry;
        }
    }
}

// CLI interface
if (php_sapi_name() === 'cli') {
    $processor = new ReactionProcessor();
    
    $command = $argv[1] ?? 'start';
    
    switch ($command) {
        case 'start':
            $processor->start();
            break;
            
        case 'stop':
            // Send SIGTERM to running process
            $pidFile = __DIR__ . '/processor.pid';
            if (file_exists($pidFile)) {
                $pid = file_get_contents($pidFile);
                if ($pid && function_exists('posix_kill')) {
                    posix_kill($pid, SIGTERM);
                    echo "Stop signal sent to process {$pid}\n";
                } else {
                    echo "Could not stop process\n";
                }
            } else {
                echo "Processor is not running\n";
            }
            break;
            
        case 'status':
            $queueManager = new QueueManager();
            $status = $queueManager->getQueueStatus();
            echo "Queue Status:\n";
            echo "- Queued posts: {$status['queued_posts']}\n";
            echo "- Active posts: {$status['active_posts']}\n";
            echo "- Completed posts: {$status['completed_posts']}\n";
            echo "- Total posts: {$status['total_posts']}\n";
            break;
            
        default:
            echo "Usage: php reaction_processor.php [start|stop|status]\n";
            break;
    }
}
