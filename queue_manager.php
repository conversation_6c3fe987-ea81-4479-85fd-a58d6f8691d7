<?php
/**
 * Queue Management System for Multibot Delayed Reactions
 * Handles persistent storage and queue operations for delayed reaction processing
 */

class QueueManager {
    private $queueDir;
    private $lockFile;
    
    public function __construct() {
        $this->queueDir = __DIR__ . '/queue_data';
        $this->lockFile = $this->queueDir . '/queue.lock';
        $this->ensureDirectoryExists();
    }
    
    /**
     * Ensure queue directory exists
     */
    private function ensureDirectoryExists() {
        if (!is_dir($this->queueDir)) {
            mkdir($this->queueDir, 0755, true);
        }
    }
    
    /**
     * Acquire file lock for queue operations
     */
    private function acquireLock() {
        $lockHandle = fopen($this->lockFile, 'w');
        if (!flock($lockHandle, LOCK_EX)) {
            fclose($lockHandle);
            return false;
        }
        return $lockHandle;
    }
    
    /**
     * Release file lock
     */
    private function releaseLock($lockHandle) {
        if ($lockHandle) {
            flock($lockHandle, LOCK_UN);
            fclose($lockHandle);
        }
    }
    
    /**
     * Add a new post to the delayed reaction queue
     */
    public function queuePost($chatId, $messageId, $emojiPool) {
        $lockHandle = $this->acquireLock();
        if (!$lockHandle) {
            return false;
        }
        
        try {
            $postId = $chatId . '_' . $messageId;
            $queueFile = $this->queueDir . '/queue.json';
            $progressFile = $this->queueDir . '/progress.json';
            
            // Load existing queue
            $queue = $this->loadJsonFile($queueFile, []);
            $progress = $this->loadJsonFile($progressFile, []);
            
            // Check if post already exists
            if (isset($progress[$postId])) {
                $this->releaseLock($lockHandle);
                return false; // Post already in progress or completed
            }
            
            // Add to queue if not already there
            if (!in_array($postId, $queue)) {
                $queue[] = $postId;
            }
            
            // Initialize progress tracking
            $progress[$postId] = [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'emoji_pool' => $emojiPool,
                'status' => 'queued',
                'current_bot' => 1,
                'next_reaction_time' => time(),
                'created_at' => time(),
                'reactions_sent' => []
            ];
            
            // Save updated data
            $this->saveJsonFile($queueFile, $queue);
            $this->saveJsonFile($progressFile, $progress);
            
            $this->releaseLock($lockHandle);
            return true;
            
        } catch (Exception $e) {
            $this->releaseLock($lockHandle);
            error_log("Queue error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get the next post ready for processing
     */
    public function getNextReadyPost() {
        $lockHandle = $this->acquireLock();
        if (!$lockHandle) {
            return null;
        }
        
        try {
            $queueFile = $this->queueDir . '/queue.json';
            $progressFile = $this->queueDir . '/progress.json';
            
            $queue = $this->loadJsonFile($queueFile, []);
            $progress = $this->loadJsonFile($progressFile, []);
            
            $currentTime = time();
            
            // Find the first post that's ready for the next reaction
            foreach ($queue as $postId) {
                if (!isset($progress[$postId])) {
                    continue;
                }
                
                $postData = $progress[$postId];
                
                // Skip completed posts
                if ($postData['status'] === 'completed') {
                    continue;
                }
                
                // Check if it's time for the next reaction
                if ($postData['next_reaction_time'] <= $currentTime) {
                    $this->releaseLock($lockHandle);
                    return [
                        'post_id' => $postId,
                        'chat_id' => $postData['chat_id'],
                        'message_id' => $postData['message_id'],
                        'emoji_pool' => $postData['emoji_pool'],
                        'current_bot' => $postData['current_bot']
                    ];
                }
            }
            
            $this->releaseLock($lockHandle);
            return null;
            
        } catch (Exception $e) {
            $this->releaseLock($lockHandle);
            error_log("Queue read error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Mark a reaction as sent and update progress
     */
    public function markReactionSent($postId, $botId, $emoji, $success = true) {
        $lockHandle = $this->acquireLock();
        if (!$lockHandle) {
            return false;
        }
        
        try {
            $queueFile = $this->queueDir . '/queue.json';
            $progressFile = $this->queueDir . '/progress.json';
            
            $queue = $this->loadJsonFile($queueFile, []);
            $progress = $this->loadJsonFile($progressFile, []);
            
            if (!isset($progress[$postId])) {
                $this->releaseLock($lockHandle);
                return false;
            }
            
            $postData = &$progress[$postId];
            
            // Record the reaction
            $postData['reactions_sent'][$botId] = [
                'emoji' => $emoji,
                'success' => $success,
                'timestamp' => time()
            ];
            
            // Update progress
            if ($success) {
                $postData['current_bot'] = $botId + 1;
                $postData['next_reaction_time'] = time() + 20; // 20 second delay
                
                // Check if all bots have reacted
                if ($botId >= 44) {
                    $postData['status'] = 'completed';
                    $postData['completed_at'] = time();
                    
                    // Remove from queue
                    $queue = array_values(array_filter($queue, function($id) use ($postId) {
                        return $id !== $postId;
                    }));
                }
            } else {
                // On failure, retry the same bot after 5 seconds
                $postData['next_reaction_time'] = time() + 5;
            }
            
            // Save updated data
            $this->saveJsonFile($queueFile, $queue);
            $this->saveJsonFile($progressFile, $progress);
            
            $this->releaseLock($lockHandle);
            return true;
            
        } catch (Exception $e) {
            $this->releaseLock($lockHandle);
            error_log("Progress update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get queue status for monitoring
     */
    public function getQueueStatus() {
        $queueFile = $this->queueDir . '/queue.json';
        $progressFile = $this->queueDir . '/progress.json';
        
        $queue = $this->loadJsonFile($queueFile, []);
        $progress = $this->loadJsonFile($progressFile, []);
        
        $status = [
            'queued_posts' => count($queue),
            'total_posts' => count($progress),
            'completed_posts' => 0,
            'active_posts' => 0
        ];
        
        foreach ($progress as $postData) {
            if ($postData['status'] === 'completed') {
                $status['completed_posts']++;
            } else {
                $status['active_posts']++;
            }
        }
        
        return $status;
    }
    
    /**
     * Clean up old completed posts (older than 24 hours)
     */
    public function cleanupOldPosts() {
        $lockHandle = $this->acquireLock();
        if (!$lockHandle) {
            return false;
        }
        
        try {
            $progressFile = $this->queueDir . '/progress.json';
            $progress = $this->loadJsonFile($progressFile, []);
            
            $cutoffTime = time() - (24 * 60 * 60); // 24 hours ago
            $cleaned = 0;
            
            foreach ($progress as $postId => $postData) {
                if ($postData['status'] === 'completed' && 
                    isset($postData['completed_at']) && 
                    $postData['completed_at'] < $cutoffTime) {
                    unset($progress[$postId]);
                    $cleaned++;
                }
            }
            
            $this->saveJsonFile($progressFile, $progress);
            $this->releaseLock($lockHandle);
            
            return $cleaned;
            
        } catch (Exception $e) {
            $this->releaseLock($lockHandle);
            error_log("Cleanup error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Load JSON file with error handling
     */
    private function loadJsonFile($filename, $default = []) {
        if (!file_exists($filename)) {
            return $default;
        }
        
        $content = file_get_contents($filename);
        if ($content === false) {
            return $default;
        }
        
        $data = json_decode($content, true);
        return $data !== null ? $data : $default;
    }
    
    /**
     * Save data to JSON file
     */
    private function saveJsonFile($filename, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT);
        return file_put_contents($filename, $json, LOCK_EX) !== false;
    }
}
